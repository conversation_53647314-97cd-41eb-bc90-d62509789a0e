import React from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title:
    "Solar Power Systems Design & Installation | Renewable Energy Solutions | JS Consultants",
  description:
    "Professional solar power engineering services including rooftop solar, ground mount systems, grid-tie solutions, and battery storage. Expert solar consultants in Chennai with sustainable energy solutions.",
  keywords:
    "solar power systems, solar panel installation, renewable energy, rooftop solar, grid-tie systems, solar battery storage, solar consultants Chennai, solar energy design, photovoltaic systems, sustainable energy solutions",
  openGraph: {
    title: "Expert Solar Power Systems Design & Installation - JS Consultants",
    description:
      "Sustainable solar energy solutions designed to reduce operational costs and environmental impact while ensuring reliable power generation.",
    type: "website",
    locale: "en_IN",
    url: "https://jsconsultants.com/services/solar",
    siteName: "JS Consultants",
    images: [
      {
        url: "/images/services/solar-og.jpg",
        width: 1200,
        height: 630,
        alt: "Solar Power Systems by JS Consultants",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Solar Power Systems Design & Installation - JS Consultants",
    description:
      "Professional solar energy solutions for cost reduction, sustainability, and reliable renewable power generation.",
    images: ["/images/services/solar-twitter.jpg"],
  },
  alternates: {
    canonical: "https://jsconsultants.com/services/solar",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  name: "Solar Power Systems Design & Installation",
  description:
    "Professional solar power engineering services including rooftop solar, ground mount systems, grid-tie solutions, and battery storage.",
  provider: {
    "@type": "Organization",
    name: "JS Consultants",
    url: "https://jsconsultants.com",
    logo: "https://jsconsultants.com/logo.png",
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+91-98765-43210",
      contactType: "customer service",
      areaServed: "IN",
      availableLanguage: ["English", "Tamil", "Hindi"],
    },
  },
  areaServed: {
    "@type": "Country",
    name: "India",
  },
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: "Solar Power Engineering Services",
    itemListElement: [
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Rooftop Solar Systems",
          description:
            "Design and installation of rooftop solar panel systems for residential and commercial buildings with grid-tie capabilities.",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Solar Battery Storage",
          description:
            "Advanced battery storage solutions for solar power systems to provide backup power and energy independence.",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Solar Farm Development",
          description:
            "Large-scale solar farm design and development for utility-scale renewable energy generation projects.",
        },
      },
    ],
  },
};

export default function SolarPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div>page</div>
    </>
  );
}
