import React from 'react';
import { Metadata } from 'next';


export const metadata: Metadata = {
  title: 'Data Center Engineering Services | Critical Infrastructure Design | JS Consultants',
  description: 'Professional data center engineering services including power systems, cooling solutions, fire protection, and critical infrastructure design. Expert data center consultants in Chennai with mission-critical expertise.',
  keywords: 'data center engineering, critical infrastructure, data center design, server room cooling, UPS systems, data center power, precision cooling, data center fire protection, mission critical facilities, data center consultants Chennai',
  openGraph: {
    title: 'Expert Data Center Engineering Services - JS Consultants',
    description: 'Comprehensive data center engineering solutions for mission-critical infrastructure including power, cooling, and protection systems.',
    type: 'website',
    locale: 'en_IN',
    url: 'https://jsconsultants.com/services/data-center',
    siteName: 'JS Consultants',
    images: [
      {
        url: '/images/services/data-center-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Data Center Engineering Services by JS Consultants',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Data Center Engineering Services - JS Consultants',
    description: 'Professional data center engineering for mission-critical infrastructure with reliable power, cooling, and protection systems.',
    images: ['/images/services/data-center-twitter.jpg'],
  },
  alternates: {
    canonical: 'https://jsconsultants.com/services/data-center',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Data Center Engineering Services',
  description: 'Professional data center engineering services including power systems, cooling solutions, fire protection, and critical infrastructure design.',
  provider: {
    '@type': 'Organization',
    name: 'JS Consultants',
    url: 'https://jsconsultants.com',
    logo: 'https://jsconsultants.com/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+91-98765-43210',
      contactType: 'customer service',
      areaServed: 'IN',
      availableLanguage: ['English', 'Tamil', 'Hindi'],
    },
  },
  areaServed: {
    '@type': 'Country',
    name: 'India',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'Data Center Engineering Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Critical Power Systems',
          description: 'Design and installation of redundant power systems including UPS, generators, and power distribution for data centers.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Precision Cooling Systems',
          description: 'Advanced cooling solutions for data centers including CRAC units, hot/cold aisle containment, and liquid cooling systems.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Data Center Infrastructure',
          description: 'Complete data center infrastructure design including raised floors, cable management, and environmental monitoring systems.',
        },
      },
    ],
  },
};

export default function DataCenterPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div>Data Center Page</div>
    </>
  );
}
