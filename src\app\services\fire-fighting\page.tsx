import React from 'react';
import { Metadata } from 'next';


export const metadata: Metadata = {
  title: 'Fire Fighting & Protection Systems | Fire Safety Engineering | JS Consultants',
  description: 'Professional fire protection engineering services including fire detection, sprinkler systems, fire suppression, smoke management, and emergency evacuation systems. Expert fire safety consultants in Chennai.',
  keywords: 'fire protection systems, fire fighting, fire detection, sprinkler systems, fire suppression, smoke management, fire safety engineering, fire alarm systems, emergency evacuation, fire consultants Chennai',
  openGraph: {
    title: 'Expert Fire Protection & Safety Systems - JS Consultants',
    description: 'Comprehensive fire safety and protection systems designed to safeguard property, personnel, and comply with all fire safety regulations.',
    type: 'website',
    locale: 'en_IN',
    url: 'https://jsconsultants.com/services/fire-fighting',
    siteName: 'JS Consultants',
    images: [
      {
        url: '/images/services/fire-fighting-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Fire Protection Systems by JS Consultants',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Fire Protection & Safety Systems - JS Consultants',
    description: 'Professional fire protection engineering solutions for comprehensive fire safety and emergency response systems.',
    images: ['/images/services/fire-fighting-twitter.jpg'],
  },
  alternates: {
    canonical: 'https://jsconsultants.com/services/fire-fighting',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Fire Fighting & Protection Systems',
  description: 'Professional fire protection engineering services including fire detection, sprinkler systems, fire suppression, smoke management, and emergency evacuation systems.',
  provider: {
    '@type': 'Organization',
    name: 'JS Consultants',
    url: 'https://jsconsultants.com',
    logo: 'https://jsconsultants.com/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+91-98765-43210',
      contactType: 'customer service',
      areaServed: 'IN',
      availableLanguage: ['English', 'Tamil', 'Hindi'],
    },
  },
  areaServed: {
    '@type': 'Country',
    name: 'India',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'Fire Protection Engineering Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Fire Detection Systems',
          description: 'Advanced fire detection systems with smoke, heat, and flame detectors for early fire warning and response.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Sprinkler Systems',
          description: 'Automatic sprinkler systems design and installation for effective fire suppression and property protection.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Fire Suppression Systems',
          description: 'Specialized fire suppression systems including gas suppression, foam systems, and water mist systems.',
        },
      },
    ],
  },
};

export default function FireFightingPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div>page</div>
    </>
  );
}
