import React from 'react';
import { Metadata } from 'next';


export const metadata: Metadata = {
  title: 'HVAC Systems Design & Installation | Climate Control Solutions | JS Consultants',
  description: 'Professional HVAC engineering services including air conditioning, ventilation, heating systems, and indoor air quality solutions. Expert HVAC consultants in Chennai with energy-efficient designs.',
  keywords: 'HVAC systems, air conditioning design, ventilation systems, heating systems, indoor air quality, climate control, HVAC consultants Chennai, energy efficient HVAC, ductwork design, HVAC installation',
  openGraph: {
    title: 'Expert HVAC Systems Design & Installation - JS Consultants',
    description: 'Comprehensive HVAC engineering solutions for optimal comfort, energy efficiency, and indoor air quality management in commercial and residential projects.',
    type: 'website',
    locale: 'en_IN',
    url: 'https://jsconsultants.com/services/hvac',
    siteName: 'JS Consultants',
    images: [
      {
        url: '/images/services/hvac-og.jpg',
        width: 1200,
        height: 630,
        alt: 'HVAC Systems Design by JS Consultants',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'HVAC Systems Design & Installation - JS Consultants',
    description: 'Professional HVAC engineering solutions for optimal climate control, energy efficiency, and indoor air quality.',
    images: ['/images/services/hvac-twitter.jpg'],
  },
  alternates: {
    canonical: 'https://jsconsultants.com/services/hvac',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'HVAC Systems Design & Installation',
  description: 'Professional HVAC engineering services including air conditioning, ventilation, heating systems, and indoor air quality solutions.',
  provider: {
    '@type': 'Organization',
    name: 'JS Consultants',
    url: 'https://jsconsultants.com',
    logo: 'https://jsconsultants.com/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+91-98765-43210',
      contactType: 'customer service',
      areaServed: 'IN',
      availableLanguage: ['English', 'Tamil', 'Hindi'],
    },
  },
  areaServed: {
    '@type': 'Country',
    name: 'India',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'HVAC Engineering Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Air Conditioning Systems',
          description: 'Design and installation of energy-efficient air conditioning systems for optimal comfort and climate control.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Ventilation Systems',
          description: 'Advanced ventilation solutions for optimal indoor air quality and compliance with building codes.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Ductwork Design',
          description: 'Efficient ductwork design and installation for optimal air distribution and energy performance.',
        },
      },
    ],
  },
};

export default function HVACPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
     <div>page</div>
    </>
  );
}
