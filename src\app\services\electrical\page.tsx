import React from 'react';
import { Metadata } from 'next';


export const metadata: Metadata = {
  title: 'Electrical Engineering Services | Power Systems Design & Installation | JS Consultants',
  description: 'Professional electrical engineering services including power distribution, lighting design, emergency systems, and electrical safety solutions. Expert electrical consultants in Chennai with 15+ years experience.',
  keywords: 'electrical engineering, power distribution systems, lighting design, electrical safety, motor control centers, emergency power systems, electrical consultants Chennai, electrical design services, power system analysis, electrical installation',
  openGraph: {
    title: 'Expert Electrical Engineering Services - JS Consultants',
    description: 'Comprehensive electrical engineering solutions for residential, commercial, and industrial projects. Power distribution, lighting design, and electrical safety systems.',
    type: 'website',
    locale: 'en_IN',
    url: 'https://jsconsultants.com/services/electrical',
    siteName: 'JS Consultants',
    images: [
      {
        url: '/images/services/electrical-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Electrical Engineering Services by JS Consultants',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Electrical Engineering Services - JS Consultants',
    description: 'Professional electrical engineering solutions including power systems, lighting design, and safety systems for all project types.',
    images: ['/images/services/electrical-twitter.jpg'],
  },
  alternates: {
    canonical: 'https://jsconsultants.com/services/electrical',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Electrical Engineering Services',
  description: 'Professional electrical engineering services including power distribution, lighting design, emergency systems, and electrical safety solutions.',
  provider: {
    '@type': 'Organization',
    name: 'JS Consultants',
    url: 'https://jsconsultants.com',
    logo: 'https://jsconsultants.com/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+91-98765-43210',
      contactType: 'customer service',
      areaServed: 'IN',
      availableLanguage: ['English', 'Tamil', 'Hindi'],
    },
  },
  areaServed: {
    '@type': 'Country',
    name: 'India',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'Electrical Engineering Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Power Distribution Systems',
          description: 'Design and installation of electrical power distribution systems for commercial and industrial facilities.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Lighting Design',
          description: 'Energy-efficient lighting design and control systems for optimal illumination and energy savings.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Emergency Power Systems',
          description: 'Backup power solutions including UPS systems and emergency generators for critical applications.',
        },
      },
    ],
  },
};

export default function ElectricalPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div>page</div>
    </>
  );
}
