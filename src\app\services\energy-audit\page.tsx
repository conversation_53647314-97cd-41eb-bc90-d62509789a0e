import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Energy Audit Services | Energy Efficiency Assessment | JS Consultants',
  description: 'Professional energy audit services to identify energy savings opportunities, reduce operational costs, and improve building performance. Expert energy consultants in Chennai with comprehensive energy assessments.',
  keywords: 'energy audit, energy efficiency, energy assessment, energy savings, building performance, energy consultants Chennai, energy management, carbon footprint reduction, energy optimization, sustainability consulting',
  openGraph: {
    title: 'Expert Energy Audit & Efficiency Services - JS Consultants',
    description: 'Comprehensive energy audits to identify savings opportunities, reduce costs, and improve building performance with sustainable energy solutions.',
    type: 'website',
    locale: 'en_IN',
    url: 'https://jsconsultants.com/services/energy-audit',
    siteName: 'JS Consultants',
    images: [
      {
        url: '/images/services/energy-audit-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Energy Audit Services by JS Consultants',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Energy Audit & Efficiency Services - JS Consultants',
    description: 'Professional energy audits for cost reduction, efficiency improvement, and sustainable building performance.',
    images: ['/images/services/energy-audit-twitter.jpg'],
  },
  alternates: {
    canonical: 'https://jsconsultants.com/services/energy-audit',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// JSON-LD structured data for SEO
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Energy Audit Services',
  description: 'Professional energy audit services to identify energy savings opportunities, reduce operational costs, and improve building performance.',
  provider: {
    '@type': 'Organization',
    name: 'JS Consultants',
    url: 'https://jsconsultants.com',
    logo: 'https://jsconsultants.com/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+91-98765-43210',
      contactType: 'customer service',
      areaServed: 'IN',
      availableLanguage: ['English', 'Tamil', 'Hindi'],
    },
  },
  areaServed: {
    '@type': 'Country',
    name: 'India',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'Energy Audit Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Comprehensive Energy Assessment',
          description: 'Detailed energy audits to identify inefficiencies and opportunities for energy savings and cost reduction.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Energy Management Systems',
          description: 'Implementation of energy management systems for continuous monitoring and optimization of energy consumption.',
        },
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Sustainability Consulting',
          description: 'Expert consulting on sustainable energy practices and green building certifications for environmental compliance.',
        },
      },
    ],
  },
};

export default function EnergyAuditPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div>page</div>
    </>
  );
}
